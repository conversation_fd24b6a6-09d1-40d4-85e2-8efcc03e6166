# <PERSON><PERSON><PERSON> Blick Development Workflow Guide

## PayloadCMS + Supabase + Vercel Multi-Environment Setup

⚠️ **CRITICAL: DATA SAFETY FIRST** ⚠️
This guide prioritizes data safety above all else. Every step includes backup procedures.
With hundreds of articles at stake, we take zero risks with data loss.

**ADAPTED FOR:** PayloadCMS + Supabase + Solo Development + Manual Deployment

---

## STEP 1: Install and Verify Supabase CLI

### 1.1 Install Supabase CLI

```bash
# Option 1: Using npm (recommended for Node.js projects)
npm install -g supabase

# Option 2: Using Homebrew (macOS)
brew install supabase/tap/supabase
```

### 1.2 Verify Installation

```bash
supabase --version
# Should show version 1.x.x or higher
```

### 1.3 Login to Supabase

```bash
supabase login
# This will open browser to get your personal access token
# Or manually get token from: https://supabase.com/dashboard/account/tokens
```

### 1.4 Verify Your Current Local Setup

```bash
# Check if Supabase is already initialized (you already have this)
ls -la supabase/
# You should see config.toml

# Check current local status
supabase status
# This shows if local Supabase is running
```

**✅ CHECKPOINT:** CLI installed, logged in, and can see your existing local setup.

---

## STEP 2: Verify Local Supabase (Your Development Database)

### 2.1 Start Local Supabase (if not running)

```bash
# From your project root
supabase start
# This boots Postgres, Auth, Storage in Docker
```

### 2.2 Verify Local Setup

```bash
# Check status
supabase status

# You should see something like:
# API URL: http://localhost:54321
# DB URL: postgresql://postgres:postgres@localhost:54322/postgres
# Studio URL: http://localhost:54323
```

### 2.3 Test PayloadCMS Connection

```bash
# Make sure your local environment works
pnpm dev
# Visit http://localhost:3000/admin
# Verify you can see your articles and data
```

**✅ CHECKPOINT:** Local Supabase running, PayloadCMS connected, data visible.

---

## STEP 3: Create Preview Supabase Project (Cloud)

### 3.1 Create Project

1. Go to https://supabase.com/dashboard
2. Click "New Project"
3. Name it: `borsenblick-preview`
4. Choose Free plan
5. Select region closest to your users
6. Wait for project creation (2-3 minutes)

### 3.2 Collect Credentials

After project creation, go to Settings > API:

```bash
# Copy these values - you'll need them:
Project Reference ID: [abc123def456]
Project URL: https://[abc123def456].supabase.co
anon public key: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
service_role key: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**⚠️ SECURITY NOTE:** Keep service_role key secret - it has admin access!

**✅ CHECKPOINT:** Preview project created, credentials collected.

---

## STEP 4: Setup Environment Files for Multi-Environment

### 4.1 Create Local Environment File

```bash
# Create .env.local (for local development)
touch .env.local
```

Add to `.env.local`:

```env
# Local Supabase (from supabase status)
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Your existing PayloadCMS variables
DATABASE_URI=postgresql://postgres:postgres@localhost:54322/postgres
PAYLOAD_SECRET=your-existing-secret
OPENAI_API_KEY=your-existing-key
FIRECRAWL_API_KEY=your-existing-key
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
# ... all other existing variables from your .env
```

### 4.2 Create Preview Environment File

```bash
# Create .env.preview.local (for preview environment)
touch .env.preview.local
```

Add to `.env.preview.local`:

```env
# Preview Supabase (from step 3.2)
NEXT_PUBLIC_SUPABASE_URL=https://[your-preview-ref].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=[your-preview-anon-key]
SUPABASE_SERVICE_ROLE_KEY=[your-preview-service-key]

# Preview PayloadCMS database (will be same as Supabase)
DATABASE_URI=postgresql://postgres:[password]@db.[your-preview-ref].supabase.co:5432/postgres
PAYLOAD_SECRET=your-existing-secret
OPENAI_API_KEY=your-existing-key
FIRECRAWL_API_KEY=your-existing-key
NEXT_PUBLIC_SERVER_URL=https://your-vercel-preview-url.vercel.app
# ... all other existing variables
```

### 4.3 Add Environment Switching Scripts

Add to `package.json` scripts section:

```json
{
  "scripts": {
    "env:local": "cp .env.local .env && echo '✅ Switched to LOCAL environment'",
    "env:preview": "cp .env.preview.local .env && echo '✅ Switched to PREVIEW environment'",
    "backup-local-db": "bash scripts/backup-local-db.sh",
    "backup:preview": "bash scripts/backup-preview-db.sh"
  }
}
```

**Note:** The backup scripts use the existing comprehensive backup system that creates organized backups with restore scripts.

### 4.4 Update .gitignore

```bash
# Add to .gitignore
echo ".env.local" >> .gitignore
echo ".env.preview.local" >> .gitignore
echo "backups/" >> .gitignore
```

**✅ CHECKPOINT:** Environment files created, switching scripts ready.

---

## STEP 5: Link Supabase CLI to Preview Project

### 5.1 Link CLI to Preview (Direct Linking - No Profiles Needed)

```bash
# Link directly to your preview project
supabase link --project-ref [your-preview-ref]
# Use the Project Reference ID from step 3.2
# Example: supabase link --project-ref vucefescwpcdbpjlccea
```

### 5.2 Verify Link

```bash
# Check which project you're linked to
supabase projects list
# Look for the ● symbol next to your preview project

# Test the connection
supabase db dump --data-only -f test_backup.sql
# This should create a backup from your preview database
```

### 5.3 Understanding CLI vs App Connections

**IMPORTANT:** CLI linking and your app's database connection are **separate systems**:

- **CLI Commands** (`supabase db dump`, `supabase db push`, etc.):
  - Use the **linked project** (currently Preview)
  - Independent of your app's environment variables

- **Your Next.js App** (`npm run dev`):
  - Uses **environment variables** from `.env.local` or `.env.preview.local`
  - Local development → Local Docker containers
  - Preview deployment → Preview database

**✅ CHECKPOINT:** CLI linked to preview project, connection verified.

---

## STEP 6: Setup Vercel-Supabase Integration

### 6.1 Connect Integration

1. Go to https://vercel.com/dashboard
2. Select your project
3. Go to Settings > Integrations
4. Find "Supabase" and click "Add Integration"
5. Select your Vercel project
6. Select your `borsenblick-preview` Supabase project
7. This automatically injects environment variables into Vercel

### 6.2 Verify Integration

1. Go to Vercel Settings > Environment Variables
2. You should see Supabase variables automatically added
3. Make sure they're set for "Preview" deployments

**✅ CHECKPOINT:** Vercel connected to preview Supabase project.

---

## STEP 7: Verify and Test Backup Scripts

### 7.1 Check Existing Backup Scripts

You should already have these backup scripts working:

```bash
# Check existing scripts
ls -la scripts/backup-*.sh

# You should see:
# scripts/backup-local-db.sh    (backs up local Docker containers)
# scripts/backup-preview-db.sh  (backs up CLI-linked preview project)
```

### 7.2 Test Both Backup Systems

```bash
# Test local backup (backs up your development data)
pnpm backup-local-db
# This creates: supabase/backups/local_db_[branch]_[timestamp]/

# Test preview backup (backs up remote preview database)
pnpm backup:preview
# This creates: supabase/backups/preview_db_[branch]_[timestamp]/
```

### 7.3 Understanding the Two Backup Systems

**Local Backup** (`pnpm backup-local-db`):

- Backs up your **local Docker containers**
- Contains your **development data** (articles, RSS feeds, etc.)
- Works regardless of CLI linking
- Rich with actual content

**Preview Backup** (`pnpm backup:preview`):

- Backs up **CLI-linked project** (currently Preview)
- Contains **remote database** (likely empty initially)
- Requires CLI to be linked to preview project
- Clean environment for testing

### 7.4 Key Insights from Testing

**✅ VERIFIED:** Both backup systems work independently:

- Local backup captures rich development data (articles, RSS feeds, users)
- Preview backup captures clean remote environment (empty schema)
- CLI linking works without profiles (direct project linking)
- Environment variables and CLI linking are separate systems

**✅ CHECKPOINT:** Backup systems tested and working correctly.

### 7.3 Create Data Sync Script

Create `scripts/sync-local-to-preview.sh`:

```bash
#!/bin/bash
set -e

echo "⚠️  WARNING: This will overwrite preview database data!"
echo "📋 Make sure you've backed up preview data first."
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Cancelled"
    exit 1
fi

echo "🔄 Syncing local data to preview..."

# Create temporary backup
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
TMP_FILE="tmp/local_data_$TIMESTAMP.sql"
mkdir -p tmp

# Dump local data (adjust table names as needed)
supabase db dump --data-only \
  -t articles -t categories -t tags -t users \
  -f $TMP_FILE

# Restore to preview
supabase db restore $TMP_FILE --profile preview

echo "✅ Data sync complete"
echo "🗑️  Cleaning up temporary file"
rm $TMP_FILE
```

### 7.4 Make Scripts Executable

```bash
chmod +x scripts/backup-preview.sh
chmod +x scripts/sync-local-to-preview.sh
```

**✅ CHECKPOINT:** Backup and sync scripts created and ready.

---

## STEP 8: Test the Complete Workflow

### 8.1 Test Local Environment

```bash
# Switch to local
pnpm env:local

# Start development
pnpm dev

# Verify everything works locally
```

### 8.2 Test Preview Environment Setup

```bash
# Switch to preview environment
pnpm env:preview

# Test connection (this should fail initially - that's expected)
pnpm dev
# You'll see database connection errors - this is normal
```

### 8.3 Push Schema to Preview

```bash
# First, create a backup of current local state
pnpm backup:local

# Push your current schema to preview
supabase db push --profile preview

# This creates the tables in preview database
```

### 8.4 Test Preview Environment

```bash
# Now test preview environment
pnpm env:preview
pnpm dev

# Visit http://localhost:3000/admin
# You should see empty database (no articles yet)
```

**✅ CHECKPOINT:** Both environments working, schema synced.

---

## STEP 9: Daily Development Workflow

### 9.1 Feature Development (Local)

```bash
# Always start with local environment
pnpm env:local

# Create feature branch
git checkout -b feature/your-feature-name

# Develop and test locally
pnpm dev

# Commit changes
git add .
git commit -m "Add your feature"
```

### 9.2 Merge to Development Branch

```bash
# Switch to development
git checkout development

# Merge your feature
git merge feature/your-feature-name

# Test on development
pnpm env:local
pnpm dev
```

### 9.3 Deploy to Preview (Manual)

```bash
# ALWAYS backup preview first
./scripts/backup-preview.sh

# Switch to preview branch
git checkout preview

# Merge development
git merge development

# Push schema changes to preview
supabase db push --profile preview

# Switch environment and test locally first
pnpm env:preview
pnpm dev
# Test that everything works

# Push to trigger Vercel deployment
git push origin preview
```

### 9.4 Sync Data (When Needed)

```bash
# Only when you need preview to have your local data
./scripts/sync-local-to-preview.sh
```

**✅ CHECKPOINT:** Complete workflow tested and documented.

---

## EMERGENCY PROCEDURES

### Data Recovery

```bash
# Restore preview from backup
supabase db restore scripts/backups/preview_backup_YYYYMMDD_HHMMSS.sql --profile preview

# Restore local from backup
pnpm restore-local-db YYYYMMDD_HHMMSS
```

### Environment Issues

```bash
# Reset to local environment
pnpm env:local

# Check what environment you're in
echo $NEXT_PUBLIC_SUPABASE_URL
```

### Schema Issues

```bash
# Reset preview database (DANGER!)
supabase db reset --profile preview

# Re-push schema
supabase db push --profile preview
```

---

## NEXT STEPS

After this setup works perfectly:

1. Add GitHub Actions for automation
2. Setup production environment
3. Add monitoring and alerts
4. Implement automated backups

**Remember:** Always backup before making changes, especially in preview!
