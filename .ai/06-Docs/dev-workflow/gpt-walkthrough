# <PERSON><PERSON><PERSON>lick Development Workflow Guide
## PayloadCMS + Supabase + Vercel Multi-Environment Setup

⚠️ **CRITICAL: DATA SAFETY FIRST** ⚠️
This guide prioritizes data safety above all else. Every step includes backup procedures.
With hundreds of articles at stake, we take zero risks with data loss.

Below is a complete, end-to-end playbook that starts with your current setup and ends with:
	•	a preview Supabase project with full backup procedures,
	•	a preview Vercel environment that always talks to that project,
	•	a local workflow that keeps code, schema, and (when you decide) rows in sync,
	•	comprehensive backup strategies for all environments.

**ADAPTED FOR:** PayloadCMS + Supabase + Solo Development + Manual Deployment

Follow the steps once; afterwards you’ll only repeat the daily workflow at the end.

⸻

1 Install and log in to the Supabase CLI (local)

npm install -g supabase         # or: brew install supabase/tap/supabase
supabase login                  # paste your personal access token

The CLI is required for local Docker, migrations, and remote pushes.  ￼

⸻

2 Spin up local Supabase (development database)

supabase init        # only if the project has no supabase/ folder yet
supabase start       # boots Postgres, Auth, Storage in Docker

The local project is your sandbox. All branches except preview and main will point here.

⸻

3 Create the preview Supabase project (cloud)
	1.	Dashboard → New Project → name it borsenblick-preview → Free plan.
	2.	Copy the Project Ref, SUPABASE_URL, anon key, and (server-only) service_role key.

⸻

4 Connect Supabase ↔ Vercel for preview builds
	1.	Vercel → Integrations → +Add Integration → Supabase.
	2.	Pick your Vercel project, choose the borsenblick-preview Supabase project.
The integration injects the URL, anon key, and service key into every preview build automatically.  ￼

Why not link production now?
The integration can manage one Supabase project per Vercel project ￼. We’ll give that slot to preview, because preview builds run on every push.

⸻

5 Local environment files

.env.local            # local Docker creds          (git-ignored)
.env.preview.local    # preview project creds       (git-ignored)
.env.example          # template committed to Git

.env.local

NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=local-anon
SUPABASE_SERVICE_ROLE_KEY=local-service

.env.preview.local

NEXT_PUBLIC_SUPABASE_URL=https://<preview-ref>.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=<preview-anon>
SUPABASE_SERVICE_ROLE_KEY=<preview-service>

Add helper scripts:

// package.json
"scripts": {
  "env:local":   "cp .env.local .env && echo 'Local DB ready'",
  "env:preview": "cp .env.preview.local .env && echo 'Preview DB ready'"
}

Run npm run env:local on every branch except preview; run npm run env:preview whenever you check out preview.

⸻

6 Link the CLI to the preview project

supabase link --project-ref <PREVIEW_REF> --profile preview

You can now run any command against that project with --profile preview.  ￼

⸻

7 GitHub repository secrets (once)
	•	SUPABASE_ACCESS_TOKEN – same token you pasted during supabase login.
	•	SUPA_PREVIEW_REF – the Project Ref for borsenblick-preview.

GitHub → Settings → Secrets → Actions → New secret.

⸻

8 Git branch strategy

Branch	Database during local dev	Deployed to	Purpose
feature/*	local Docker	—	Daily coding
development	local Docker	—	Team integration
preview	preview project	Vercel Preview URL	Client review
main	production (later)	borsenblick.com	Live


⸻

9 Create and commit migrations on development

# after changing tables in Studio or SQL
supabase db diff -o supabase/migrations
git add supabase/migrations
git commit -m "add comments table"

db diff writes plain-SQL files for Git to track.  ￼

⸻

10 Move code and schema to the preview branch

git checkout preview
git merge development                     # bring code across
npm run env:preview                       # point env vars at preview DB
supabase db push --profile preview        # apply migrations in cloud
git push origin preview                   # triggers Vercel preview build

db push only runs new migration files on the linked database.  ￼

⸻

11 Automate step 10 (optional but recommended)

Create .github/workflows/preview.yml:

name: Preview Deploy
on:
  push:
    branches: [preview]

env:
  SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
  SUPA_PREVIEW_REF:     ${{ secrets.SUPA_PREVIEW_REF }}

jobs:
  migrate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: supabase/setup-cli@v2          # installs CLI  [oai_citation:6‡Supabase](https://supabase.com/partners/vercel?utm_source=chatgpt.com)
      - run: supabase link --project-ref $SUPA_PREVIEW_REF
      - run: supabase db push
  deploy:
    needs: migrate
    uses: vercel/vercel-action@v3
    with:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      scope: preview

Now git push origin preview applies migrations and deploys the site without you typing anything.

⸻

12 Copy local rows to preview (only when you want)

# dump selected tables from local Docker
mkdir -p tmp
supabase db dump --data-only \
  -t articles -t categories -t tags \
  -f tmp/local_data.sql

# restore into preview
supabase db restore tmp/local_data.sql --profile preview

Wrap those two lines in scripts/sync_local_to_preview.sh so you don’t mistype them.

⸻

13 Verify
	1.	Open your Vercel preview URL.
	2.	In DevTools → Network, Supabase calls should hit https://<preview-ref>.supabase.co.
	3.	Visit Supabase Studio (preview project); counts in articles, categories, tags match local.

⸻

14 Daily workflow (summary)
	1.	Code on feature branches → merge into development → test against local DB.
	2.	Ready for client?

git checkout preview
git merge development
git push origin preview       # CI handles migrations & deploy

(If you need data: run scripts/sync_local_to_preview.sh first.)

	3.	Send the Vercel preview link to the client.

Production promotion and backups can slot in later without changing any of the above.

⸻

Follow these steps once and you’ll have a clean, repeatable pipeline from your laptop to a safe preview environment—no risk to production, no mystery connections. Ping me whenever you hit the next milestone.
