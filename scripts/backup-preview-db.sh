#!/bin/bash

# Preview Database Backup
# Usage: pnpm backup:preview

set -e

BACKUP_BASE_DIR="supabase/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
# Get current git branch and sanitize for filename
BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
# Replace characters that aren't filesystem-safe with underscores
BRANCH_SAFE=$(echo "$BRANCH" | sed 's/[^a-zA-Z0-9._-]/_/g')

# Create backup-specific subfolder with full filename inside
BACKUP_NAME="preview_db_${BRANCH_SAFE}_$TIMESTAMP"
BACKUP_DIR="$BACKUP_BASE_DIR/$BACKUP_NAME"
BACKUP_FILE="$BACKUP_DIR/$BACKUP_NAME.sql"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "🗄️ Backing up PREVIEW Supabase database..."

# Check if preview profile is linked
if ! supabase status --profile preview > /dev/null 2>&1; then
    echo "❌ Error: Preview profile is not linked"
    echo "Please link preview profile with: supabase link --project-ref [your-preview-ref] --profile preview"
    exit 1
fi

# Create separate backups following Supabase best practices
ROLES_FILE="${BACKUP_FILE%.sql}_roles.sql"
SCHEMA_FILE="${BACKUP_FILE%.sql}_schema.sql"
DATA_FILE="${BACKUP_FILE%.sql}_data.sql"

echo "📋 Creating roles backup..."
supabase db dump --profile preview --role-only -f "$ROLES_FILE"

echo "🏗️ Creating schema backup..."
supabase db dump --profile preview -f "$SCHEMA_FILE"

echo "📊 Creating data backup..."
supabase db dump --profile preview --data-only --use-copy -f "$DATA_FILE"

# For convenience, also create a combined restore script
RESTORE_SCRIPT="${BACKUP_FILE%.sql}_restore.sh"
cat > "$RESTORE_SCRIPT" << EOF
#!/bin/bash
# Auto-generated restore script for PREVIEW backup: ${BRANCH_SAFE}_$TIMESTAMP
# Usage: ./$(basename "$RESTORE_SCRIPT")

set -euo pipefail

echo "🔄 Restoring PREVIEW database from backup: ${BRANCH_SAFE}_$TIMESTAMP"

# Check if preview profile is linked
if ! supabase status --profile preview > /dev/null 2>&1; then
    echo "❌ Error: Preview profile is not linked"
    echo "Please link preview profile first"
    exit 1
fi

echo "⚠️ ⚠️ ⚠️  DANGER: This will replace your PREVIEW database with the backup."
echo "⚠️ This affects your live preview environment!"
echo "Are you absolutely sure? (y/N)"
read -r confirm

if [ "\$confirm" != "y" ] && [ "\$confirm" != "Y" ]; then
    echo "❌ Restore cancelled"
    exit 0
fi

echo "🗄️ Resetting PREVIEW database..."
supabase db reset --profile preview

echo "📦 Restoring from backup files..."
supabase db restore "$ROLES_FILE" --profile preview
supabase db restore "$SCHEMA_FILE" --profile preview  
supabase db restore "$DATA_FILE" --profile preview

echo "✅ PREVIEW database restored successfully!"
echo "🎯 Restored from backup: ${BRANCH_SAFE}_$TIMESTAMP"
echo "🌐 Check your preview environment to verify the restore"
EOF

chmod +x "$RESTORE_SCRIPT"

# Create a simple info file
cat > "$BACKUP_DIR/preview_db_${BRANCH_SAFE}_$TIMESTAMP.info" << EOF
Supabase PREVIEW Database Backup
===============================
Created: $(date)
Branch: $BRANCH
Timestamp: $TIMESTAMP
Git commit: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

⚠️ WARNING: This is a PREVIEW environment backup
⚠️ Restoring affects your live preview database

Files created:
- $ROLES_FILE (Database roles)
- $SCHEMA_FILE (Database schema)  
- $DATA_FILE (Database data with COPY statements)
- $RESTORE_SCRIPT (Auto-generated restore script)

Restore options:
==============
Option 1: Use the restore script (CAREFUL!)
  ./$RESTORE_SCRIPT

Option 2: Manual restore (CAREFUL!)
  supabase db restore [file] --profile preview

File sizes:
- Roles: $(du -h "$ROLES_FILE" | cut -f1)
- Schema: $(du -h "$SCHEMA_FILE" | cut -f1) 
- Data: $(du -h "$DATA_FILE" | cut -f1)

SAFETY REMINDER:
===============
- Always backup before making changes
- Preview affects your live preview environment
- Test restores carefully
- Consider data sync from local instead of restore
EOF

echo "✅ PREVIEW database backed up successfully!"
echo "📁 Files created:"
echo "   📋 Roles: $ROLES_FILE"
echo "   🏗️ Schema: $SCHEMA_FILE" 
echo "   📊 Data: $DATA_FILE"
echo "   🔄 Restore script: $RESTORE_SCRIPT"
echo "📋 Info file: $BACKUP_DIR/preview_db_${BRANCH_SAFE}_$TIMESTAMP.info"
echo ""
echo "⚠️  SAFETY REMINDER: This backup is from your PREVIEW environment"
echo "🚀 Quick restore: ./$RESTORE_SCRIPT (USE WITH CAUTION!)"
echo "💡 Consider using data sync instead of restore for safety"
